import discord
import aiohttp
import re
from typing import Optional, Dict, Any

from discord.ext import commands

from utilities import decorators


async def setup(bot):
    await bot.add_cog(Social(bot))


class CarbonModal(discord.ui.Modal, title="Create Carbon Image"):
    def __init__(self, cog):
        super().__init__()
        self.cog = cog

    code = discord.ui.TextInput(
        label="Enter your code",
        style=discord.TextStyle.paragraph,
        required=True,
        max_length=2000
    )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer()

        try:
            # Try Ryzumi API first
            data = await self.cog.fetch_api("/tool/carbon", {"code": self.code.value})

            if data and data.get("url"):
                embed = discord.Embed(
                    color=0x323339,
                    title="Your Carbon Image",
                    description="Here is the Carbon image of your code:"
                )
                embed.set_image(url=data["url"])
                await interaction.followup.send(embed=embed)
            else:
                # Fallback: Create a simple code block embed
                code_content = self.code.value
                if len(code_content) > 1900:
                    code_content = code_content[:1900] + "..."

                embed = discord.Embed(
                    color=0x323339,
                    title="Code Block",
                    description=f"```\n{code_content}\n```"
                )
                embed.set_footer(text="Carbon API unavailable - showing code block instead")
                await interaction.followup.send(embed=embed)

        except Exception:
            embed = discord.Embed(
                color=0xFF0000,
                title="Error",
                description="There was an error while processing your code."
            )
            await interaction.followup.send(embed=embed, ephemeral=True)


class CarbonView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=60)
        self.cog = cog

    @discord.ui.button(label="Create Carbon", style=discord.ButtonStyle.primary)
    async def create_carbon(self, interaction: discord.Interaction, _button: discord.ui.Button):
        modal = CarbonModal(self.cog)
        await interaction.response.send_modal(modal)


class PaginationView(discord.ui.View):
    def __init__(self, ctx, data, embed_generator, timeout=60):
        super().__init__(timeout=timeout)
        self.ctx = ctx
        self.data = data
        self.embed_generator = embed_generator
        self.current_index = 0
        
        # Disable buttons if only one item
        if len(data) <= 1:
            self.prev_button.disabled = True
            self.next_button.disabled = True
        else:
            self.prev_button.disabled = True

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id != self.ctx.author.id:
            await interaction.response.send_message(
                "You can't use these buttons.", ephemeral=True
            )
            return False
        return True

    @discord.ui.button(label="◀ Previous", style=discord.ButtonStyle.secondary)
    async def prev_button(self, interaction: discord.Interaction, _button: discord.ui.Button):
        self.current_index -= 1

        # Update button states
        self.prev_button.disabled = (self.current_index == 0)
        self.next_button.disabled = (self.current_index == len(self.data) - 1)

        embed = self.embed_generator(self.current_index)
        await interaction.response.edit_message(embed=embed, view=self)

    @discord.ui.button(label="Next ▶", style=discord.ButtonStyle.secondary)
    async def next_button(self, interaction: discord.Interaction, _button: discord.ui.Button):
        self.current_index += 1
        
        # Update button states
        self.prev_button.disabled = (self.current_index == 0)
        self.next_button.disabled = (self.current_index == len(self.data) - 1)
        
        embed = self.embed_generator(self.current_index)
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_timeout(self):
        # Disable all buttons when timeout
        for item in self.children:
            item.disabled = True
        
        try:
            await self.message.edit(view=self)
        except:
            pass


class Social(commands.Cog):
    """
    Social media and web tools.
    """

    def __init__(self, bot):
        self.bot = bot
        self.session = None

    async def cog_load(self):
        self.session = aiohttp.ClientSession()

    async def cog_unload(self):
        if self.session:
            await self.session.close()

    async def fetch_api(self, endpoint: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Fetch data from Ryzumi API with fallback handling"""
        try:
            url = f"https://api.ryzumi.com{endpoint}"
            timeout = aiohttp.ClientTimeout(total=10)
            async with self.session.post(url, json=params or {}, timeout=timeout) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    print(f"API returned status {response.status} for {endpoint}")
                    return None
        except aiohttp.ClientConnectorError:
            print(f"Connection failed to Ryzumi API for {endpoint}")
            return None
        except Exception as e:
            print(f"API Error for {endpoint}: {e}")
            return None

    def create_error_embed(self, message: str, title: str = "Error") -> discord.Embed:
        """Create a standardized error embed"""
        return discord.Embed(
            title=title,
            description=message,
            color=0xFF0000
        )

    @decorators.command(brief="Create a Carbon image of your code")
    async def carbon(self, ctx):
        """
        Usage: {0}carbon
        Output: Opens a modal to input code and generates a Carbon image
        """
        view = CarbonView(self)
        await ctx.send("Click the button below to create a Carbon image:", view=view)

    @decorators.command(brief="Search Google for the provided query")
    async def google(self, ctx, *, query: str):
        """
        Usage: {0}google <query>
        Example: {0}google discord.py documentation
        Output: Shows Google search results
        """
        if not query:
            embed = self.create_error_embed(
                "Please provide a valid search query.",
                "Google Search Error"
            )
            return await ctx.send(embed=embed)

        async with ctx.typing():
            data = await self.fetch_api("/search/google", {"query": query})

            if not data or len(data) == 0:
                # Fallback: Create a Google search link
                search_url = f"https://www.google.com/search?q={query.replace(' ', '+')}"
                embed = discord.Embed(
                    title="Google Search",
                    description=f"API unavailable. [Click here to search Google for: {query}]({search_url})",
                    color=0x323339,
                    timestamp=discord.utils.utcnow()
                )
                return await ctx.send(embed=embed)

            results = []
            for result in data:
                results.append(f"**[{result['title']}]({result['link']})**\n{result['description']}")

            title = f"Google Search Results for: {query[:30]}{'...' if len(query) > 30 else ''}"
            embed = discord.Embed(
                title=title,
                description="\n\n".join(results),
                color=0x323339,
                timestamp=discord.utils.utcnow()
            )

            await ctx.send(embed=embed)

    @decorators.command(brief="Download Instagram media", aliases=["igdl"])
    async def instagram_dl(self, ctx, link: str):
        """
        Usage: {0}instagram-dl <link>
        Alias: {0}igdl
        Example: {0}instagram-dl https://instagram.com/p/...
        Output: Downloads Instagram media
        """
        async with ctx.typing():
            data = await self.fetch_api("/downloader/igdl", {"url": link})
            
            if not data or not data.get("status"):
                embed = self.create_error_embed(
                    "Invalid Instagram link",
                    "Instagram-dl Error"
                )
                return await ctx.send(embed=embed)

            # Send the media URL directly since we can't use ContainerBuilder
            media_url = data["data"][0]["url"]
            embed = discord.Embed(
                title="Instagram Media Downloader",
                description=f"[Download Media]({media_url})",
                color=0x323339
            )
            await ctx.send(embed=embed)

    @decorators.command(brief="Download Twitter/X media", aliases=["xdl", "twitterdl"])
    async def x_dl(self, ctx, link: str):
        """
        Usage: {0}x-dl <link>
        Alias: {0}xdl, {0}twitterdl
        Example: {0}x-dl https://x.com/user/status/...
        Output: Downloads Twitter/X media
        """
        async with ctx.typing():
            data = await self.fetch_api("/downloader/twitter", {"url": link})
            
            if not data or not data.get("status"):
                embed = self.create_error_embed(
                    "Invalid X link",
                    "X-dl Error"
                )
                return await ctx.send(embed=embed)

            # Send the media URL directly
            media_url = data["media"][0]["url"]
            embed = discord.Embed(
                title="X Media Downloader",
                description=f"[Download Media]({media_url})",
                color=0x323339
            )
            await ctx.send(embed=embed)

    @decorators.command(brief="Search for images on Pinterest")
    async def pinterest(self, ctx, *, query: str):
        """
        Usage: {0}pinterest <query>
        Example: {0}pinterest nature wallpapers
        Output: Shows Pinterest image results with navigation
        """
        async with ctx.typing():
            data = await self.fetch_api("/search/pinterest", {"query": query})

            if not data or len(data) == 0:
                embed = self.create_error_embed(
                    f"No valid image results found for: **{query}**",
                    "Pinterest Search"
                )
                return await ctx.send(embed=embed)

            def generate_embed(index):
                image = data[index]
                embed = discord.Embed(
                    color=0x323339,
                    description=f"[Click here to download the image]({image['link']})"
                )
                embed.set_image(url=image["directLink"])
                embed.set_footer(text=f"Image {index + 1} of {len(data)} | Searched for: {query}")
                return embed

            view = PaginationView(ctx, data, generate_embed)
            embed = generate_embed(0)
            message = await ctx.send(embed=embed, view=view)
            view.message = message

    @decorators.command(brief="Get IP location information", aliases=["iplocation"])
    async def ip_location(self, ctx, ip: str):
        """
        Usage: {0}ip-location <ip>
        Alias: {0}iplocation
        Example: {0}ip-location *******
        Output: Shows location and details of the IP address
        """
        # Validate IP address format
        ip_pattern = r'^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'

        if not re.match(ip_pattern, ip):
            embed = self.create_error_embed(
                "Please provide a valid IP address.",
                "IP Location Error"
            )
            return await ctx.send(embed=embed)

        async with ctx.typing():
            data = await self.fetch_api("/tool/iplocation", {"ip": ip})

            if not data or data.get("ipInfo", {}).get("error"):
                embed = self.create_error_embed(
                    "Invalid IP address provided.",
                    "IP Location Error"
                )
                return await ctx.send(embed=embed)

            ip_info = data["ipInfo"]
            embed = discord.Embed(
                title="IP Location Information",
                description=f"Result for IP: {ip_info['ip']}",
                color=0x323339,
                timestamp=discord.utils.utcnow()
            )

            embed.add_field(
                name="Network",
                value=f"- **IP:** {ip_info['ip']}\n"
                      f"- **Network:** {ip_info['network']}\n"
                      f"- **Version:** {ip_info['version']}\n"
                      f"- **ASN:** {ip_info['asn']}\n"
                      f"- **ISP:** {ip_info['org']}",
                inline=False
            )

            embed.add_field(
                name="Region",
                value=f"- **Country:** {ip_info['country_name']}\n"
                      f"- **Region:** {ip_info['region']}\n"
                      f"- **City:** {ip_info['city']}\n"
                      f"- **Latitude:** {ip_info['latitude']}\n"
                      f"- **Longitude:** {ip_info['longitude']}",
                inline=False
            )

            await ctx.send(embed=embed)

    @decorators.command(brief="Get live wallpapers from Moe Walls", aliases=["wallpaper"])
    async def wallpaper_search(self, ctx, *, query: str):
        """
        Usage: {0}wallpaper-search <query>
        Alias: {0}wallpaper
        Example: {0}wallpaper-search anime
        Output: Shows wallpaper results with navigation
        """
        async with ctx.typing():
            data = await self.fetch_api("/search/wallpaper-moe", {"query": query})

            if not data or not data.get("result") or len(data["result"]) == 0:
                embed = self.create_error_embed(
                    f"No valid wallpaper results found for: **{query}**",
                    "Wallpaper Search"
                )
                return await ctx.send(embed=embed)

            wallpapers = data["result"]

            def generate_embed(index):
                wallpaper = wallpapers[index]
                embed = discord.Embed(
                    title=wallpaper["title"],
                    url=wallpaper["link"],
                    description=":arrow_up: Click link above to view the wallpaper.",
                    color=0x323339
                )
                embed.set_image(url=wallpaper["wallpaper"])
                embed.set_footer(text=f"Wallpaper {index + 1} of {len(wallpapers)} | Searched for: {query}")
                return embed

            view = PaginationView(ctx, wallpapers, generate_embed)
            embed = generate_embed(0)
            message = await ctx.send(embed=embed, view=view)
            view.message = message

    @decorators.command(brief="Search YouTube for videos", aliases=["yt"])
    async def youtube_search(self, ctx, *, query: str):
        """
        Usage: {0}youtube-search <query>
        Alias: {0}yt
        Example: {0}youtube-search python tutorial
        Output: Shows YouTube video results with navigation
        """
        async with ctx.typing():
            data = await self.fetch_api("/search/yt", {"query": query})

            if not data or not data.get("videos") or len(data["videos"]) == 0:
                embed = self.create_error_embed(
                    f"No valid video results found for: **{query}**",
                    "YouTube Search"
                )
                return await ctx.send(embed=embed)

            videos = data["videos"]

            def generate_embed(index):
                video = videos[index]
                embed = discord.Embed(
                    title=video["title"],
                    url=video["url"],
                    description=video["description"],
                    color=0x323339
                )
                embed.set_image(url=video["thumbnail"])

                embed.add_field(name="Views", value=video["views"], inline=True)
                embed.add_field(name="Duration", value=video["duration"]["timestamp"], inline=True)
                embed.add_field(
                    name="Channel",
                    value=f"[{video['author']['name']}]({video['author']['url']})",
                    inline=True
                )

                embed.set_footer(text=f"Video {index + 1} of {len(videos)} | Searched for: {query}")
                return embed

            view = PaginationView(ctx, videos, generate_embed)
            embed = generate_embed(0)
            message = await ctx.send(embed=embed, view=view)
            view.message = message
