import discord
import aiohttp
import re
from typing import Optional, Dict, Any

from discord.ext import commands

from utilities import decorators


async def setup(bot):
    await bot.add_cog(Social(bot))


class CarbonModal(discord.ui.Modal, title="Create Carbon Image"):
    def __init__(self, cog):
        super().__init__()
        self.cog = cog

    code = discord.ui.TextInput(
        label="Enter your code",
        style=discord.TextStyle.paragraph,
        required=True,
        max_length=2000
    )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer()

        try:
            # Create carbon image link
            carbon_url = await self.cog.create_carbon_image(self.code.value)

            if carbon_url:
                embed = discord.Embed(
                    color=0x323339,
                    title="Your Carbon Image",
                    description=f"[Click here to view and download your Carbon image]({carbon_url})"
                )
                embed.add_field(
                    name="Instructions",
                    value="1. Click the link above\n2. The image will load automatically\n3. Right-click and save the image",
                    inline=False
                )
                await interaction.followup.send(embed=embed)
            else:
                # Fallback: Create a simple code block embed
                code_content = self.code.value
                if len(code_content) > 1900:
                    code_content = code_content[:1900] + "..."

                embed = discord.Embed(
                    color=0x323339,
                    title="Code Block",
                    description=f"```\n{code_content}\n```"
                )
                embed.set_footer(text="Carbon service unavailable - showing code block instead")
                await interaction.followup.send(embed=embed)

        except Exception:
            embed = discord.Embed(
                color=0xFF0000,
                title="Error",
                description="There was an error while processing your code."
            )
            await interaction.followup.send(embed=embed, ephemeral=True)


class CarbonView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=60)
        self.cog = cog

    @discord.ui.button(label="Create Carbon", style=discord.ButtonStyle.primary)
    async def create_carbon(self, interaction: discord.Interaction, _button: discord.ui.Button):
        modal = CarbonModal(self.cog)
        await interaction.response.send_modal(modal)


class PaginationView(discord.ui.View):
    def __init__(self, ctx, data, embed_generator, timeout=60):
        super().__init__(timeout=timeout)
        self.ctx = ctx
        self.data = data
        self.embed_generator = embed_generator
        self.current_index = 0
        
        # Disable buttons if only one item
        if len(data) <= 1:
            self.prev_button.disabled = True
            self.next_button.disabled = True
        else:
            self.prev_button.disabled = True

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id != self.ctx.author.id:
            await interaction.response.send_message(
                "You can't use these buttons.", ephemeral=True
            )
            return False
        return True

    @discord.ui.button(label="◀ Previous", style=discord.ButtonStyle.secondary)
    async def prev_button(self, interaction: discord.Interaction, _button: discord.ui.Button):
        self.current_index -= 1

        # Update button states
        self.prev_button.disabled = (self.current_index == 0)
        self.next_button.disabled = (self.current_index == len(self.data) - 1)

        embed = self.embed_generator(self.current_index)
        await interaction.response.edit_message(embed=embed, view=self)

    @discord.ui.button(label="Next ▶", style=discord.ButtonStyle.secondary)
    async def next_button(self, interaction: discord.Interaction, _button: discord.ui.Button):
        self.current_index += 1
        
        # Update button states
        self.prev_button.disabled = (self.current_index == 0)
        self.next_button.disabled = (self.current_index == len(self.data) - 1)
        
        embed = self.embed_generator(self.current_index)
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_timeout(self):
        # Disable all buttons when timeout
        for item in self.children:
            item.disabled = True
        
        try:
            await self.message.edit(view=self)
        except:
            pass


class Social(commands.Cog):
    """
    Social media and web tools.
    """

    def __init__(self, bot):
        self.bot = bot
        self.session = None

    async def cog_load(self):
        self.session = aiohttp.ClientSession()

    async def cog_unload(self):
        if self.session:
            await self.session.close()

    async def fetch_youtube_search(self, query: str) -> Optional[Dict[str, Any]]:
        """Search YouTube using a working API"""
        try:
            # Using YouTube's search without API key (limited but functional)
            search_url = f"https://www.youtube.com/results?search_query={query.replace(' ', '+')}"
            return {"search_url": search_url, "query": query}
        except Exception as e:
            print(f"YouTube search error: {e}")
            return None

    async def fetch_pinterest_search(self, query: str) -> Optional[Dict[str, Any]]:
        """Search Pinterest using direct links"""
        try:
            search_url = f"https://www.pinterest.com/search/pins/?q={query.replace(' ', '%20')}"
            return {"search_url": search_url, "query": query}
        except Exception as e:
            print(f"Pinterest search error: {e}")
            return None

    async def create_carbon_image(self, code: str) -> Optional[str]:
        """Create carbon image using carbon.now.sh"""
        try:
            # Use carbon.now.sh direct link
            import urllib.parse
            encoded_code = urllib.parse.quote(code)
            carbon_url = f"https://carbon.now.sh/?bg=rgba%2874%2C144%2C226%2C1%29&t=material&wt=none&l=auto&width=680&ds=true&dsyoff=20px&dsblur=68px&wc=true&wa=true&pv=56px&ph=56px&ln=false&fl=1&fm=Hack&fs=14px&lh=133%25&si=false&es=2x&wm=false&code={encoded_code}"
            return carbon_url
        except Exception as e:
            print(f"Carbon creation error: {e}")
            return None

    def create_error_embed(self, message: str, title: str = "Error") -> discord.Embed:
        """Create a standardized error embed"""
        return discord.Embed(
            title=title,
            description=message,
            color=0xFF0000
        )

    @decorators.command(brief="Create a Carbon image of your code")
    async def carbon(self, ctx):
        """
        Usage: {0}carbon
        Output: Opens a modal to input code and generates a Carbon image
        """
        view = CarbonView(self)
        await ctx.send("Click the button below to create a Carbon image:", view=view)

    @decorators.command(brief="Search Google for the provided query")
    async def google(self, ctx, *, query: str):
        """
        Usage: {0}google <query>
        Example: {0}google discord.py documentation
        Output: Shows Google search results
        """
        if not query:
            embed = self.create_error_embed(
                "Please provide a valid search query.",
                "Google Search Error"
            )
            return await ctx.send(embed=embed)

        async with ctx.typing():
            # Create a Google search link
            search_url = f"https://www.google.com/search?q={query.replace(' ', '+')}"
            embed = discord.Embed(
                title="Google Search",
                description=f"[Click here to search Google for: {query}]({search_url})",
                color=0x323339,
                timestamp=discord.utils.utcnow()
            )
            embed.add_field(
                name="Search Query",
                value=f"`{query}`",
                inline=False
            )
            embed.set_footer(text="Click the link above to view search results")
            await ctx.send(embed=embed)

    @decorators.command(brief="Download Instagram media", aliases=["igdl"])
    async def instagram_dl(self, ctx, link: str):
        """
        Usage: {0}instagram-dl <link>
        Alias: {0}igdl
        Example: {0}instagram-dl https://instagram.com/p/...
        Output: Provides alternative download methods
        """
        async with ctx.typing():
            embed = discord.Embed(
                title="Instagram Media Downloader",
                description="Download service temporarily unavailable. Try these alternatives:",
                color=0x323339
            )
            embed.add_field(
                name="Alternative Methods",
                value="• Use [SaveInsta](https://saveinsta.app/)\n"
                      "• Use [InstaDownloader](https://instadownloader.co/)\n"
                      "• Use [SnapInsta](https://snapinsta.app/)",
                inline=False
            )
            embed.add_field(
                name="Your Link",
                value=f"`{link}`",
                inline=False
            )
            embed.set_footer(text="Copy your link and paste it into one of the alternative services")
            await ctx.send(embed=embed)

    @decorators.command(brief="Download Twitter/X media", aliases=["xdl", "twitterdl"])
    async def x_dl(self, ctx, link: str):
        """
        Usage: {0}x-dl <link>
        Alias: {0}xdl, {0}twitterdl
        Example: {0}x-dl https://x.com/user/status/...
        Output: Provides alternative download methods
        """
        async with ctx.typing():
            embed = discord.Embed(
                title="X/Twitter Media Downloader",
                description="Download service temporarily unavailable. Try these alternatives:",
                color=0x323339
            )
            embed.add_field(
                name="Alternative Methods",
                value="• Use [TwitterVideoDownloader](https://twittervideodownloader.com/)\n"
                      "• Use [SaveTweet](https://savetweet.net/)\n"
                      "• Use [DownloadTwitterVideo](https://www.downloadtwittervideo.com/)",
                inline=False
            )
            embed.add_field(
                name="Your Link",
                value=f"`{link}`",
                inline=False
            )
            embed.set_footer(text="Copy your link and paste it into one of the alternative services")
            await ctx.send(embed=embed)

    @decorators.command(brief="Search for images on Pinterest")
    async def pinterest(self, ctx, *, query: str):
        """
        Usage: {0}pinterest <query>
        Example: {0}pinterest nature wallpapers
        Output: Shows Pinterest search link
        """
        async with ctx.typing():
            data = await self.fetch_pinterest_search(query)

            if not data:
                embed = self.create_error_embed(
                    f"Could not create search for: **{query}**",
                    "Pinterest Search"
                )
                return await ctx.send(embed=embed)

            embed = discord.Embed(
                title="Pinterest Search",
                description=f"[Click here to search Pinterest for: {query}]({data['search_url']})",
                color=0x323339,
                timestamp=discord.utils.utcnow()
            )
            embed.add_field(
                name="Search Query",
                value=f"`{query}`",
                inline=False
            )
            embed.set_footer(text="Click the link above to view Pinterest results")
            await ctx.send(embed=embed)

    @decorators.command(brief="Get IP location information", aliases=["iplocation"])
    async def ip_location(self, ctx, ip: str):
        """
        Usage: {0}ip-location <ip>
        Alias: {0}iplocation
        Example: {0}ip-location *******
        Output: Shows location and details of the IP address
        """
        # Validate IP address format
        ip_pattern = r'^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'

        if not re.match(ip_pattern, ip):
            embed = self.create_error_embed(
                "Please provide a valid IP address.",
                "IP Location Error"
            )
            return await ctx.send(embed=embed)

        async with ctx.typing():
            try:
                # Use ip-api.com which is free and doesn't require API key
                url = f"http://ip-api.com/json/{ip}"
                async with self.session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()

                        if data.get("status") == "success":
                            embed = discord.Embed(
                                title="IP Location Information",
                                description=f"Result for IP: {ip}",
                                color=0x323339,
                                timestamp=discord.utils.utcnow()
                            )

                            embed.add_field(
                                name="Network",
                                value=f"- **IP:** {ip}\n"
                                      f"- **ISP:** {data.get('isp', 'Unknown')}\n"
                                      f"- **Organization:** {data.get('org', 'Unknown')}\n"
                                      f"- **AS:** {data.get('as', 'Unknown')}",
                                inline=False
                            )

                            embed.add_field(
                                name="Location",
                                value=f"- **Country:** {data.get('country', 'Unknown')}\n"
                                      f"- **Region:** {data.get('regionName', 'Unknown')}\n"
                                      f"- **City:** {data.get('city', 'Unknown')}\n"
                                      f"- **Timezone:** {data.get('timezone', 'Unknown')}\n"
                                      f"- **Coordinates:** {data.get('lat', 'Unknown')}, {data.get('lon', 'Unknown')}",
                                inline=False
                            )

                            await ctx.send(embed=embed)
                        else:
                            embed = self.create_error_embed(
                                "Invalid IP address or lookup failed.",
                                "IP Location Error"
                            )
                            await ctx.send(embed=embed)
                    else:
                        embed = self.create_error_embed(
                            "IP lookup service unavailable.",
                            "IP Location Error"
                        )
                        await ctx.send(embed=embed)
            except Exception:
                embed = self.create_error_embed(
                    "An error occurred while looking up the IP address.",
                    "IP Location Error"
                )
                await ctx.send(embed=embed)

    @decorators.command(brief="Search for wallpapers", aliases=["wallpaper"])
    async def wallpaper_search(self, ctx, *, query: str):
        """
        Usage: {0}wallpaper-search <query>
        Alias: {0}wallpaper
        Example: {0}wallpaper-search anime
        Output: Provides wallpaper search links
        """
        async with ctx.typing():
            embed = discord.Embed(
                title="Wallpaper Search",
                description=f"Search for wallpapers with query: **{query}**",
                color=0x323339,
                timestamp=discord.utils.utcnow()
            )

            # Create search URLs for different wallpaper sites
            unsplash_url = f"https://unsplash.com/s/photos/{query.replace(' ', '-')}"
            wallhaven_url = f"https://wallhaven.cc/search?q={query.replace(' ', '+')}"
            pexels_url = f"https://www.pexels.com/search/{query.replace(' ', '%20')}/"

            embed.add_field(
                name="Wallpaper Sources",
                value=f"• [Unsplash]({unsplash_url})\n"
                      f"• [Wallhaven]({wallhaven_url})\n"
                      f"• [Pexels]({pexels_url})",
                inline=False
            )
            embed.add_field(
                name="Search Query",
                value=f"`{query}`",
                inline=False
            )
            embed.set_footer(text="Click any link above to browse wallpapers")
            await ctx.send(embed=embed)

    @decorators.command(brief="Search YouTube for videos", aliases=["yt"])
    async def youtube_search(self, ctx, *, query: str):
        """
        Usage: {0}youtube-search <query>
        Alias: {0}yt
        Example: {0}youtube-search python tutorial
        Output: Shows YouTube search link
        """
        async with ctx.typing():
            data = await self.fetch_youtube_search(query)

            if not data:
                embed = self.create_error_embed(
                    f"Could not create search for: **{query}**",
                    "YouTube Search"
                )
                return await ctx.send(embed=embed)

            embed = discord.Embed(
                title="YouTube Search",
                description=f"[Click here to search YouTube for: {query}]({data['search_url']})",
                color=0x323339,
                timestamp=discord.utils.utcnow()
            )
            embed.add_field(
                name="Search Query",
                value=f"`{query}`",
                inline=False
            )
            embed.set_footer(text="Click the link above to view YouTube results")
            await ctx.send(embed=embed)
